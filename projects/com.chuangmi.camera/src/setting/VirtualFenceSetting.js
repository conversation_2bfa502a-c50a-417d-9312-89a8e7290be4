import {View, StyleSheet, ScrollView} from 'react-native';
import React, {useEffect, useState} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {stringsTo} from '../../../../globalization/Localize';

const VirtualFenceSetting = props => {
  const [virtualFenceEnabled, setVirtualFenceEnabled] = useState(true);
  const [personEnterFence, setPersonEnterFence] = useState(true);
  const [personLeaveFence, setPersonLeaveFence] = useState(false);
  const [showVirtualFence, setShowVirtualFence] = useState(false);

  useEffect(() => {
    // 初始化数据
  }, []);

  const handleVirtualFenceChange = (value) => {
    setVirtualFenceEnabled(value);
    console.log('虚拟围栏开关:', value);
  };

  const handlePersonEnterChange = (value) => {
    setPersonEnterFence(value);
    console.log('有人进入围栏:', value);
  };

  const handlePersonLeaveChange = (value) => {
    setPersonLeaveFence(value);
    console.log('有人离开围栏:', value);
  };

  const handleShowVirtualFenceChange = (value) => {
    setShowVirtualFence(value);
    console.log('显示虚拟围栏:', value);
  };

  const handleFenceAreaPress = () => {
    // 跳转到围栏区域设置页面
    console.log('跳转到围栏区域设置');
    const defaultAreaData = [22, 30, 150, 120]; // 默认区域坐标
    props.navigation.push('VirtualFenceAreaSetting', {
      areaData: defaultAreaData,
      areaType: 0,
      callback: (areaData, areaType) => {
        console.log('围栏区域编辑回调:', areaData, areaType);
        // 这里可以更新状态或执行其他操作
      }
    });
  };

  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo("virtual_Fence")}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
              props.navigation.goBack() ? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'virtual_fence_back',
          },
        ]}
        right={[]}
      />
      
      <ScrollView style={styles.scrollView}>
        {/* 设置选项区域 */}
        <View style={styles.settingsSection}>
          {/* 虚拟围栏主开关 */}
          <ListItmeWithSwitch 
            title="虚拟围栏"
            value={virtualFenceEnabled}
            onValueChange={handleVirtualFenceChange}
            accessibilityLabel={['virtual_fence_off', 'virtual_fence_on']}
          />
          
          {/* 有人进入围栏 */}
          {virtualFenceEnabled && (
            <ListItmeWithSwitch 
              title="有人进入围栏"
              subtitle="当有人进入围栏时，拍摄视频并推送"
              value={personEnterFence}
              onValueChange={handlePersonEnterChange}
              accessibilityLabel={['person_enter_off', 'person_enter_on']}
            />
          )}
          
          {/* 有人离开围栏 */}
          {virtualFenceEnabled && (
            <ListItmeWithSwitch 
              title="有人离开围栏"
              subtitle="当有人离开围栏时，拍摄视频并推送"
              value={personLeaveFence}
              onValueChange={handlePersonLeaveChange}
              accessibilityLabel={['person_leave_off', 'person_leave_on']}
            />
          )}
          
          {/* 围栏区域设置 */}
          {virtualFenceEnabled && (
            <ListItem 
              title="围栏区域"
              onPress={handleFenceAreaPress}
              accessibilityLabel="fence_area_setting"
            />
          )}
          
          {/* 显示虚拟围栏 */}
          {virtualFenceEnabled && (
            <ListItmeWithSwitch 
              title="显示虚拟围栏"
              value={showVirtualFence}
              onValueChange={handleShowVirtualFenceChange}
              accessibilityLabel={['show_fence_off', 'show_fence_on']}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  settingsSection: {
    marginTop: 20,
  },
});

export default VirtualFenceSetting;
